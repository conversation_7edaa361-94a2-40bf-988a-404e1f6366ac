/**
 * Advanced Analytics System
 * Comprehensive analytics with A/B testing, funnels, heat maps, and segmentation
 */

// Core Analytics Interfaces
interface PageViewData {
  path?: string;
  title?: string;
  referrer?: string;
  duration?: number;
}

interface ContactSubmissionData {
  name: string;
  email: string;
  subject: string;
  message: string;
  source?: string;
}

interface SystemMetricData {
  metric: string;
  value: number;
  unit?: string;
  category?: string;
  metadata?: any;
}

// Advanced Analytics Interfaces
interface ABTestVariant {
  id: number;
  name: string;
  config: any;
  trafficSplit: number;
}

interface FunnelStep {
  funnelId: number;
  stepOrder: number;
  timeSpent?: number;
  metadata?: any;
}

interface HeatMapInteraction {
  elementType: 'click' | 'hover' | 'scroll' | 'form_focus';
  xPosition?: number;
  yPosition?: number;
  scrollDepth?: number;
  element?: HTMLElement;
}

interface BehaviorEvent {
  eventType: string;
  eventName: string;
  properties?: any;
  pageUrl?: string;
  referrer?: string;
}

class AnalyticsTracker {
  private sessionId: string;
  private startTime: number;
  private isEnabled: boolean = true;
  private apiBaseUrl: string = process.env.NEXT_PUBLIC_API_URL ? `${process.env.NEXT_PUBLIC_API_URL}/api/v1` : '/api/v1';
  private userId: number | null = null;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
    this.isEnabled = this.shouldEnableAnalytics();
    this.userId = this.getUserId();

    if (this.isEnabled) {
      console.log('📊 Advanced Analytics: Enabled');
      this.initializeTracking();
    } else {
      console.log('📊 Analytics: Disabled (development mode)');
    }
  }

  private generateSessionId(): string {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private shouldEnableAnalytics(): boolean {
    // Enable analytics in production and when explicitly enabled in development
    return process.env.NODE_ENV === 'production' ||
      process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true' ||
      typeof window !== 'undefined' && window.location.hostname !== 'localhost';
  }

  private getUserId(): number | null {
    if (typeof window === 'undefined') return null;

    try {
      const token = localStorage.getItem('accessToken');
      if (token) {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const userId = payload.userId;
        return userId ? parseInt(userId, 10) : null;
      }
    } catch (error) {
      // Ignore errors
    }
    return null;
  }

  private initializeTracking(): void {
    // Set up automatic page view tracking
    if (typeof window !== 'undefined') {
      // Wait for DOM to be ready before initializing tracking
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          this.setupTracking();
        });
      } else {
        this.setupTracking();
      }
    }
  }

  private setupTracking(): void {
    // Track initial page view
    this.trackPageView();

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.trackCustomEvent('page_visibility', { state: 'hidden' });
      }
    });
  }

  // Core Analytics Methods
  public trackPageView(additionalData: Partial<PageViewData> = {}, waitForResponse: boolean = true): void {
    if (!this.isEnabled) return;

    const data: PageViewData = {
      path: window.location.pathname,
      title: document.title,
      referrer: document.referrer || undefined,
      ...additionalData
    };

    this.sendTrackingData('/analytics/track/pageview', {
      sessionId: this.sessionId,
      userId: this.userId,
      ...data
    }, waitForResponse);
  }

  public async trackContactSubmission(data: ContactSubmissionData): Promise<any> {
    if (!this.isEnabled) return Promise.resolve({ success: true, message: 'Analytics disabled' });

    return this.sendTrackingData('/analytics/track/contact', {
      sessionId: this.sessionId,
      userId: this.userId,
      ...data
    }, true);
  }

  public trackCustomEvent(eventName: string, properties: any = {}): void {
    if (!this.isEnabled) return;

    // Ensure we have valid data
    const pageUrl = typeof window !== 'undefined' ? window.location.href : undefined;

    this.sendTrackingData('/analytics/track/custom', {
      sessionId: this.sessionId,
      userId: this.userId,
      eventName,
      properties,
      timestamp: new Date().toISOString(),
      ...(pageUrl && { pageUrl })
    }, false);
  }

  public trackSystemMetric(data: SystemMetricData): void {
    if (!this.isEnabled) return;

    this.sendTrackingData('/analytics/metrics', {
      sessionId: this.sessionId,
      userId: this.userId,
      ...data,
      timestamp: new Date().toISOString()
    }, false);
  }

  public getSessionInfo(): any {
    return {
      sessionId: this.sessionId,
      startTime: this.startTime,
      isEnabled: this.isEnabled,
      userId: this.userId
    };
  }

  // Advanced Analytics Methods
  public async getABTestVariant(testId: number): Promise<ABTestVariant | null> {
    if (!this.isEnabled) return null;

    try {
      const response = await fetch(`${this.apiBaseUrl}/advanced-analytics/ab-tests/${testId}/assignment?sessionId=${this.sessionId}&userId=${this.userId || ''}`);
      if (response.ok) {
        const data = await response.json();
        return data.variant;
      }
    } catch (error) {
      console.error('Failed to get A/B test variant:', error);
    }
    return null;
  }

  public async recordABTestConversion(testId: number, goalType: string, goalValue?: number, metadata?: any): Promise<void> {
    if (!this.isEnabled) return;

    this.sendTrackingData('/advanced-analytics/ab-tests/track-conversion', {
      testId,
      sessionId: this.sessionId,
      userId: this.userId,
      goalType,
      goalValue,
      metadata,
      timestamp: new Date().toISOString()
    }, false);
  }

  public async trackFunnelStep(funnelId: number, stepOrder: number, timeSpent?: number, metadata?: any): Promise<void> {
    if (!this.isEnabled) return;

    this.sendTrackingData('/advanced-analytics/funnels/track-step', {
      funnelId,
      stepOrder,
      sessionId: this.sessionId,
      userId: this.userId,
      timeSpent,
      metadata,
      timestamp: new Date().toISOString()
    }, false);
  }

  public trackHeatMapInteraction(elementType: 'click' | 'hover' | 'scroll' | 'form_focus', element?: HTMLElement, coordinates?: { x: number, y: number }): void {
    if (!this.isEnabled) return;

    const rect = element?.getBoundingClientRect();
    const pageUrl = typeof window !== 'undefined' ? window.location.href : undefined;

    if (!pageUrl) return; // Skip if no valid page URL

    const data: any = {
      sessionId: this.sessionId,
      pageUrl,
      elementType,
      timestamp: new Date().toISOString()
    };

    if (coordinates) {
      data.xPosition = Math.round(coordinates.x);
      data.yPosition = Math.round(coordinates.y);
    } else if (rect) {
      data.xPosition = Math.round(rect.left + rect.width / 2);
      data.yPosition = Math.round(rect.top + rect.height / 2);
    }

    if (elementType === 'scroll') {
      data.scrollDepth = Math.round((window.scrollY / document.body.scrollHeight) * 100);
    }

    this.sendTrackingData('/advanced-analytics/heatmap/track', data, false);
  }

  public trackBehaviorEvent(eventType: string, eventName: string, properties?: any): void {
    if (!this.isEnabled) return;

    this.sendTrackingData('/advanced-analytics/segments/track-behavior', {
      sessionId: this.sessionId,
      eventType,
      eventName,
      properties: properties || {},
      pageUrl: window.location.href,
      referrer: document.referrer || undefined,
      timestamp: new Date().toISOString()
    }, false);
  }

  // Helper Methods
  private async sendTrackingData(endpoint: string, data: any, waitForResponse: boolean = false): Promise<any> {
    if (!this.isEnabled) return Promise.resolve();

    // Add retry logic for failed requests
    const maxRetries = 2;
    let retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        const response = await fetch(`${this.apiBaseUrl}${endpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        if (waitForResponse) {
          const result = await response.json();
          return result;
        }
        return Promise.resolve();
      } catch (error) {
        retryCount++;

        if (retryCount > maxRetries) {
          console.error(`Analytics tracking failed for ${endpoint} after ${maxRetries} retries:`, error);
          if (waitForResponse) {
            throw error;
          }
          return Promise.resolve();
        }

        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }
  }

  // Auto-tracking setup methods
  public setupHeatMapTracking(): void {
    if (!this.isEnabled || typeof window === 'undefined') return;

    // Track clicks
    document.addEventListener('click', (event) => {
      this.trackHeatMapInteraction('click', event.target as HTMLElement, {
        x: event.clientX,
        y: event.clientY
      });
    });

    // Track scroll
    let scrollTimeout: NodeJS.Timeout;
    window.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        this.trackHeatMapInteraction('scroll');
      }, 1000);
    });

    // Track form focus
    document.addEventListener('focusin', (event) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        this.trackHeatMapInteraction('form_focus', event.target as HTMLElement);
      }
    });
  }

  public setupBehaviorTracking(): void {
    if (!this.isEnabled || typeof window === 'undefined') return;

    // Track time on page
    let timeOnPage = 0;
    const timeInterval = setInterval(() => {
      timeOnPage += 30;
      this.trackBehaviorEvent('engagement', 'time_on_page', { seconds: timeOnPage });
    }, 30000);

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
      clearInterval(timeInterval);
      this.trackBehaviorEvent('engagement', 'page_exit', { timeSpent: timeOnPage });
    });

    // Track scroll depth milestones
    const scrollMilestones = [25, 50, 75, 100];
    let reachedMilestones: number[] = [];

    window.addEventListener('scroll', () => {
      const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);

      scrollMilestones.forEach(milestone => {
        if (scrollPercent >= milestone && !reachedMilestones.includes(milestone)) {
          reachedMilestones.push(milestone);
          this.trackBehaviorEvent('engagement', 'scroll_milestone', { percent: milestone });
        }
      });
    });
  }
}

// Create singleton instance
const analytics = new AnalyticsTracker();

// Initialize advanced tracking features
if (typeof window !== 'undefined') {
  // Set up heat map and behavior tracking after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      analytics.setupHeatMapTracking();
      analytics.setupBehaviorTracking();
    });
  } else {
    analytics.setupHeatMapTracking();
    analytics.setupBehaviorTracking();
  }
}

// Export for use in components
export default analytics;

// Export types for TypeScript users
export type {
  PageViewData,
  ContactSubmissionData,
  SystemMetricData,
  ABTestVariant,
  FunnelStep,
  HeatMapInteraction,
  BehaviorEvent
};

// Core Analytics Functions
export const trackPageView = (data?: Partial<PageViewData>) => analytics.trackPageView(data);
export const trackContactSubmission = (data: ContactSubmissionData) => analytics.trackContactSubmission(data);
export const trackCustomEvent = (eventName: string, properties?: any) => analytics.trackCustomEvent(eventName, properties);
export const trackSystemMetric = (data: SystemMetricData) => analytics.trackSystemMetric(data);
export const getSessionInfo = () => analytics.getSessionInfo();

// Advanced Analytics Functions
export const getABTestVariant = (testId: number) => analytics.getABTestVariant(testId);
export const recordABTestConversion = (testId: number, goalType: string, goalValue?: number, metadata?: any) =>
  analytics.recordABTestConversion(testId, goalType, goalValue, metadata);
export const trackFunnelStep = (funnelId: number, stepOrder: number, timeSpent?: number, metadata?: any) =>
  analytics.trackFunnelStep(funnelId, stepOrder, timeSpent, metadata);
export const trackHeatMapInteraction = (elementType: 'click' | 'hover' | 'scroll' | 'form_focus', element?: HTMLElement, coordinates?: { x: number, y: number }) =>
  analytics.trackHeatMapInteraction(elementType, element, coordinates);
export const trackBehaviorEvent = (eventType: string, eventName: string, properties?: any) =>
  analytics.trackBehaviorEvent(eventType, eventName, properties);
