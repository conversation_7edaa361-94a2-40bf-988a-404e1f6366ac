import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import Script from 'next/script';
import { AuthProvider } from '../lib/auth-context';
import ErrorBoundary from '../components/ErrorBoundary';
import FrontendLoggerInit from '../components/FrontendLoggerInit';
import AnalyticsInit from '../components/AnalyticsInit';
import ChatWidget from '../components/ChatWidget';
import CookieConsent from '../components/CookieConsent';
import Header from '../components/Header';
import Footer from '../components/Footer';
import './globals.css';

// Google Analytics Measurement ID from PRD

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Omnilyzer - Transform Your Business with AI-Powered Intelligence',
  description: 'Unlock 40% efficiency gains with enterprise-grade AI solutions. From intelligent automation to predictive analytics, we help 500+ companies transform operations and accelerate growth.',
  keywords: 'AI consultancy, artificial intelligence, automation, machine learning, enterprise AI, AI development, business intelligence, AI solutions',
  authors: [{ name: 'Omnilyzer Team' }],
  creator: 'O<PERSON><PERSON><PERSON><PERSON>',
  publisher: 'Omnilyzer',
  robots: 'index, follow',
  openGraph: {
    title: 'Omnilyzer - Transform Your Business with AI-Powered Intelligence',
    description: 'Unlock 40% efficiency gains with enterprise-grade AI solutions. Join 500+ companies already winning with AI.',
    url: 'https://omnilyzer.ai',
    siteName: 'Omnilyzer',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Omnilyzer - Transform Your Business with AI-Powered Intelligence',
    description: 'Unlock 40% efficiency gains with enterprise-grade AI solutions. Join 500+ companies already winning with AI.',
    creator: '@omnilyzer',
  },

};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#1e40af',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ErrorBoundary>
          <AuthProvider>
            <FrontendLoggerInit />
            <AnalyticsInit />
            <Header />
            {children}
            <Footer />
            <ChatWidget />
            <CookieConsent />
          </AuthProvider>
        </ErrorBoundary>
        {/* Google Analytics Script - Only in production */}
        {process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_GA_ID && (
          <>
            <Script
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
              strategy="afterInteractive"
            />
            <Script
              id="google-analytics"
              strategy="afterInteractive"
            >
              {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
              `}
            </Script>
          </>
        )}
      </body>
    </html>
  );
}
