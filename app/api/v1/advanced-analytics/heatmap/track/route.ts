import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // In development mode, just return success without processing
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({ success: true, message: 'Heatmap analytics disabled in development' });
    }

    // In production, you could implement actual heatmap analytics here
    const body = await request.json();
    
    // For now, just log and return success
    console.log('Heatmap analytics:', body);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Heatmap analytics error:', error);
    return NextResponse.json({ success: false, error: 'Analytics error' }, { status: 500 });
  }
}
