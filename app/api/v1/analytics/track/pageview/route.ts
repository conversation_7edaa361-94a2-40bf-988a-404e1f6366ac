import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // In development mode, just return success without processing
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({ success: true, message: 'Analytics disabled in development' });
    }

    // In production, you could implement actual analytics here
    const body = await request.json();
    
    // For now, just log and return success
    console.log('Analytics pageview:', body);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Analytics error:', error);
    return NextResponse.json({ success: false, error: 'Analytics error' }, { status: 500 });
  }
}
