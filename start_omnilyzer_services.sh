#!/bin/bash

# Omnilyzer Services Startup Script
echo "Starting Omnilyzer services..."

# Function to check if port is in use
check_port() {
    netstat -tlnp | grep ":$1 " > /dev/null
    return $?
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(netstat -tlnp | grep ":$port " | awk '{print $7}' | cut -d'/' -f1)
    if [ ! -z "$pid" ]; then
        echo "Killing process $pid on port $port"
        kill -9 $pid 2>/dev/null
        sleep 2
    fi
}

# Start Backend API (port 3001)
echo "Starting Backend API on port 3001..."
if check_port 3001; then
    echo "Port 3001 already in use, killing existing process..."
    kill_port 3001
fi
cd /var/www/omnilyzer && nohup node server.js > /var/log/omnilyzer-backend.log 2>&1 &
sleep 3

# Start Development Frontend (port 3000)
echo "Starting Development Frontend on port 3000..."
if check_port 3000; then
    echo "Port 3000 already in use, killing existing process..."
    kill_port 3000
fi
cd /var/www/omnilyzer && nohup npm run dev > /var/log/omnilyzer-dev.log 2>&1 &
sleep 5

# Start Production Frontend (port 3002)
echo "Starting Production Frontend on port 3002..."
if check_port 3002; then
    echo "Port 3002 already in use, killing existing process..."
    kill_port 3002
fi
cd /var/www/omnilyzer-prod && nohup npm run dev -- --port 3002 > /var/log/omnilyzer-prod.log 2>&1 &
sleep 5

# Check status
echo ""
echo "Service Status:"
echo "==============="
if check_port 3001; then
    echo "✅ Backend API (port 3001): Running"
else
    echo "❌ Backend API (port 3001): Not running"
fi

if check_port 3000; then
    echo "✅ Development Frontend (port 3000): Running"
else
    echo "❌ Development Frontend (port 3000): Not running"
fi

if check_port 3002; then
    echo "✅ Production Frontend (port 3002): Running"
else
    echo "❌ Production Frontend (port 3002): Not running"
fi

echo ""
echo "URLs:"
echo "====="
echo "🌐 Production:  https://omnilyzer.ai"
echo "🔧 Development: https://dev.omnilyzer.ai"
echo ""
echo "Logs:"
echo "====="
echo "📋 Backend:     tail -f /var/log/omnilyzer-backend.log"
echo "📋 Development: tail -f /var/log/omnilyzer-dev.log"
echo "📋 Production:  tail -f /var/log/omnilyzer-prod.log"
