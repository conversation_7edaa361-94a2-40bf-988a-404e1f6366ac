{"name": "omnilyzer-rebuild", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_OPTIONS='--max-old-space-size=2048' next dev -p 3000", "dev:optimized": "./scripts/start_memory_optimized_dev.sh", "build": "NODE_ENV=production DISABLE_ESLINT_PLUGIN=true ./node_modules/.bin/next build --no-lint", "start": "./node_modules/.bin/next start -p 3000", "server": "nodemon server/index.js", "server:review": "nodemon server/index.js & sleep 5 && ./scripts/startup_log_review.sh", "dev:all": "concurrently \"npm run dev\" \"npm run server\"", "dev:review": "concurrently \"npm run dev\" \"npm run server:review\"", "start:production": "npm run build && concurrently \"npm start\" \"npm run server:review\"", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "logs:review": "./scripts/automated_log_review.sh", "logs:monitor": "./scripts/monitor_logs.sh", "logs:frontend": "./scripts/monitor_frontend_logs.sh", "restart": "./scripts/restart_with_review.sh", "restart:dev": "./scripts/restart_with_review.sh dev", "restart:prod": "./scripts/restart_with_review.sh prod", "restart:backend": "./scripts/restart_with_review.sh backend"}, "dependencies": {"@aws-sdk/client-ses": "^3.840.0", "@prisma/client": "^6.10.1", "@sendgrid/mail": "^8.1.5", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.19.0", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "handlebars": "^4.7.8", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.4.3", "lucide-react": "^0.525.0", "morgan": "^1.10.0", "mysql2": "^3.9.0", "next": "^15.0.0", "node-cron": "^4.1.1", "nodemailer": "^6.10.1", "openai": "^5.8.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "postmark": "^4.0.5", "prisma": "^6.10.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "redis": "^5.5.6", "systeminformation": "^5.27.7", "validator": "^13.15.15", "winston": "^3.12.0", "ws": "^8.18.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/lru-cache": "^7.10.9", "@types/node": "^20.11.20", "@types/passport": "^1.0.16", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^8.56.0", "eslint-config-next": "^15.0.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "jsdom": "^26.1.0", "nodemon": "^3.0.3", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}