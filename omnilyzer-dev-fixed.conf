# Omnilyzer Development Server Configuration - Fixed Static Asset Serving
server {
    listen 80;
    server_name dev.omnilyzer.ai *************;

    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name dev.omnilyzer.ai *************;

    ssl_certificate /etc/nginx/ssl/omnilyzer-dev.crt;
    ssl_certificate_key /etc/nginx/ssl/omnilyzer-dev.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_stapling off;
    ssl_stapling_verify off;
    
    access_log /var/log/nginx/omnilyzer-dev-access.log;
    error_log /var/log/nginx/omnilyzer-dev-error.log;

    # Public static assets (images, favicon, etc)
    location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
        root /var/www/omnilyzer/.next/standalone;
        try_files /public/$uri /public/images/$uri /public/$uri =404;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # Next.js static files (CSS, JS, etc)
    location /_next/static {
        alias /var/www/omnilyzer/.next/standalone/.next/static;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000, immutable";
    }

    # Frontend - Next.js
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Backend API routes (specific endpoints)
    location ~ ^/api/(v1|auth|admin|analytics|contact|dashboard) {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Next.js API routes (chatbot and others) - handled by frontend
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
