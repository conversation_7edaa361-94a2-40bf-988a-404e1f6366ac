#!/bin/bash

# Omnilyzer Status Check Script
echo "Omnilyzer Services Status Check"
echo "==============================="
echo ""

# Function to check if port is in use
check_port() {
    netstat -tlnp | grep ":$1 " > /dev/null
    return $?
}

# Function to test HTTP response
test_url() {
    local url=$1
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" --max-time 10)
    echo "$response"
}

# Check ports
echo "Port Status:"
echo "============"
if check_port 3001; then
    echo "✅ Backend API (port 3001): Running"
else
    echo "❌ Backend API (port 3001): Not running"
fi

if check_port 3000; then
    echo "✅ Development Frontend (port 3000): Running"
else
    echo "❌ Development Frontend (port 3000): Not running"
fi

if check_port 3002; then
    echo "✅ Production Frontend (port 3002): Running"
else
    echo "❌ Production Frontend (port 3002): Not running"
fi

echo ""
echo "Website Status:"
echo "==============="

# Test production site
prod_status=$(test_url "https://omnilyzer.ai")
if [ "$prod_status" = "200" ]; then
    echo "✅ Production (https://omnilyzer.ai): HTTP $prod_status"
else
    echo "❌ Production (https://omnilyzer.ai): HTTP $prod_status"
fi

# Test development site
dev_status=$(test_url "https://dev.omnilyzer.ai")
if [ "$dev_status" = "200" ]; then
    echo "✅ Development (https://dev.omnilyzer.ai): HTTP $dev_status"
else
    echo "❌ Development (https://dev.omnilyzer.ai): HTTP $dev_status"
fi

echo ""
echo "System Resources:"
echo "=================="
echo "Memory Usage: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2}')"
echo "Disk Usage: $(df -h /var/www | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')"

echo ""
echo "Recent Log Entries:"
echo "==================="
if [ -f /var/log/omnilyzer-prod.log ]; then
    echo "Production (last 3 lines):"
    tail -3 /var/log/omnilyzer-prod.log 2>/dev/null || echo "No production logs found"
else
    echo "No production log file found"
fi

echo ""
echo "Quick Commands:"
echo "==============="
echo "🔄 Restart services: /var/www/omnilyzer/start_omnilyzer_services.sh"
echo "📋 View prod logs:   tail -f /var/log/omnilyzer-prod.log"
echo "📋 View dev logs:    tail -f /var/log/omnilyzer-dev.log"
echo "📋 View backend logs: tail -f /var/log/omnilyzer-backend.log"
