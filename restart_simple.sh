#!/bin/bash

# Omnilyzer Simple Restart Script
# This script safely restarts Omnilyzer services without crashing SSH or the server

set -euo pipefail

# Configuration
DEV_DIR="/var/www/omnilyzer"
PROD_DIR="/var/www/omnilyzer-prod"
LOG_DIR="/var/www/logs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Create log directory
mkdir -p "$LOG_DIR"

echo -e "${BLUE}=== Omnilyzer Safe Restart Script Started ===${NC}"

# Function to brutally kill processes on a specific port
kill_port() {
    local port=$1
    local service_name=$2
    
    echo -e "${YELLOW}Killing all processes on port $port ($service_name)...${NC}"
    
    # Find and kill all processes using the port
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        echo "Found processes: $pids"
        # Force kill immediately
        echo "$pids" | xargs -r kill -KILL 2>/dev/null || true
        sleep 1
        echo -e "${GREEN}✅ Killed processes on port $port${NC}"
    else
        echo "No processes found on port $port"
    fi
}

# Function to wait for service
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_wait=30
    local count=0
    
    echo "Waiting for $service_name on port $port..."
    
    while [ $count -lt $max_wait ]; do
        if curl -s -f "http://localhost:$port" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready${NC}"
            return 0
        fi
        sleep 2
        count=$((count + 2))
        echo -n "."
    done
    echo
    echo -e "${RED}❌ $service_name failed to start${NC}"
    return 1
}

# Step 1: Kill existing services
echo -e "${YELLOW}Step 1: Stopping existing services...${NC}"
kill_port 3000 "Development Server"
kill_port 3001 "Backend Server"
kill_port 3002 "Production Server"

# Wait for ports to be free
echo "Waiting for ports to be completely free..."
sleep 3

# Step 2: Start services
echo -e "${YELLOW}Step 2: Starting services...${NC}"

# Start development server
echo "Starting development server..."
cd "$DEV_DIR"
nohup npm run dev > "$LOG_DIR/omnilyzer-dev.log" 2>&1 &
echo "Development server started with PID: $!"

# Start production server
echo "Starting production server..."
cd "$PROD_DIR"
nohup npm run dev -- --port 3002 > "$LOG_DIR/omnilyzer-prod.log" 2>&1 &
echo "Production server started with PID: $!"

# Step 3: Wait and check status
echo -e "${YELLOW}Step 3: Waiting for services to start...${NC}"
sleep 15

echo "Service Status:"
echo "==============="

# Check services
if lsof -ti:3000 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Development Server (port 3000): Running${NC}"
else
    echo -e "${RED}❌ Development Server (port 3000): Not running${NC}"
fi

if lsof -ti:3002 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Production Server (port 3002): Running${NC}"
else
    echo -e "${RED}❌ Production Server (port 3002): Not running${NC}"
fi

echo
echo "URLs:"
echo "====="
echo "🌐 Production:  https://omnilyzer.ai"
echo "🔧 Development: https://dev.omnilyzer.ai"
echo
echo "Logs:"
echo "====="
echo "📋 Development: tail -f $LOG_DIR/omnilyzer-dev.log"
echo "📋 Production:  tail -f $LOG_DIR/omnilyzer-prod.log"

echo -e "${GREEN}=== Restart Complete ===${NC}"
