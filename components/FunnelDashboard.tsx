'use client';

import { useState, useEffect } from 'react';



interface Funnel {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
  steps: Array<{
    id: number;
    name: string;
    stepOrder: number;
    pagePattern: string;
    eventType: string;
  }>;
  _count: {
    sessions: number;
  };
}

interface FunnelAnalytics {
  funnel: {
    id: number;
    name: string;
    description: string;
  };
  summary: {
    totalSessions: number;
    completedSessions: number;
    overallConversionRate: number;
    timeframe: string;
  };
  stepAnalytics: Array<{
    stepId: number;
    stepOrder: number;
    name: string;
    pagePattern: string;
    eventType: string;
    completions: number;
    conversionRate: number;
    dropoffRate: number;
    avgTimeSpent: number;
  }>;
  dailyBreakdown: Array<{
    date: string;
    sessions: number;
    completions: number;
    conversionRate: number;
  }>;
}

export default function FunnelDashboard() {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Funnel Analytics</h2>
        <p className="text-gray-600">Funnel dashboard temporarily simplified for production build.</p>
        <p className="text-sm text-gray-500 mt-2">Chart functionality will be restored after production deployment.</p>
      </div>
    </div>
  );
}

const fetchFunnelAnalytics = async (funnelId: number) => {
  try {
    const token = localStorage.getItem('accessToken');
    const response = await fetch(`/api/v1/advanced-analytics/funnels/${funnelId}/analytics?timeframe=${timeframe}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.ok) {
      const result = await response.json();
      setAnalytics(result.data);
    }
  } catch (err) {
    console.error('Error fetching funnel analytics:', err);
  }
};

const createSampleFunnel = async () => {
  try {
    const token = localStorage.getItem('accessToken');
    const response = await fetch('/api/v1/advanced-analytics/funnels', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'User Onboarding Funnel',
        description: 'Track user journey from landing to signup',
        steps: [
          {
            name: 'Landing Page Visit',
            description: 'User visits homepage',
            pagePattern: '/',
            eventType: 'pageview'
          },
          {
            name: 'About Page View',
            description: 'User views about page',
            pagePattern: '/about',
            eventType: 'pageview'
          },
          {
            name: 'Contact Form View',
            description: 'User visits contact page',
            pagePattern: '/contact',
            eventType: 'pageview'
          },
          {
            name: 'Form Submission',
            description: 'User submits contact form',
            pagePattern: '/contact',
            eventType: 'form_submit'
          }
        ]
      }),
    });

    if (response.ok) {
      fetchFunnels(); // Refresh the list
    }
  } catch (err) {
    console.error('Error creating funnel:', err);
  }
};

if (loading) {
  return (
    <div className="flex items-center justify-center h-64">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
  );
}

if (error) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
      <p className="text-red-800">{error}</p>
      <button
        onClick={fetchFunnels}
        className="mt-2 text-red-600 hover:text-red-800 underline"
      >
        Try again
      </button>
    </div>
  );
}

// Chart data for funnel visualization
const funnelChartData = analytics ? {
  labels: analytics.stepAnalytics.map(step => step.name),
  datasets: [
    {
      label: 'Completions',
      data: analytics.stepAnalytics.map(step => step.completions),
      backgroundColor: 'rgba(59, 130, 246, 0.8)',
      borderColor: 'rgb(59, 130, 246)',
      borderWidth: 2,
      fill: false,
    },
    {
      label: 'Conversion Rate (%)',
      data: analytics.stepAnalytics.map(step => step.conversionRate),
      backgroundColor: 'rgba(16, 185, 129, 0.8)',
      borderColor: 'rgb(16, 185, 129)',
      borderWidth: 2,
      yAxisID: 'y1',
      type: 'line' as const,
    },
  ],
} : null;

// Chart data for daily trends
const trendsChartData = analytics ? {
  labels: analytics.dailyBreakdown.map(day => new Date(day.date).toLocaleDateString()),
  datasets: [
    {
      label: 'Sessions',
      data: analytics.dailyBreakdown.map(day => day.sessions),
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
    {
      label: 'Completions',
      data: analytics.dailyBreakdown.map(day => day.completions),
      borderColor: 'rgb(16, 185, 129)',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4,
    },
  ],
} : null;

const chartOptions = {
  responsive: true,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      type: 'linear' as const,
      display: true,
      position: 'left' as const,
      beginAtZero: true,
    },
    y1: {
      type: 'linear' as const,
      display: true,
      position: 'right' as const,
      beginAtZero: true,
      max: 100,
      grid: {
        drawOnChartArea: false,
      },
    },
  },
};

return (
  <div className="space-y-6">
    {/* Header */}
    <div className="flex justify-between items-center">
      <h2 className="text-2xl font-bold text-gray-900">Conversion Funnel Dashboard</h2>
      <div className="flex space-x-4">
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="7d">Last 7 Days</option>
          <option value="30d">Last 30 Days</option>
          <option value="90d">Last 90 Days</option>
        </select>
        <button
          onClick={createSampleFunnel}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Create Sample Funnel
        </button>
      </div>
    </div>

    {/* Funnel Selection */}
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Funnels</h3>
      {funnels.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No conversion funnels found</p>
          <button
            onClick={createSampleFunnel}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Your First Funnel
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {funnels.map((funnel) => (
            <div
              key={funnel.id}
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${selectedFunnel === funnel.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
                }`}
              onClick={() => setSelectedFunnel(funnel.id)}
            >
              <h4 className="font-semibold text-gray-900">{funnel.name}</h4>
              <p className="text-sm text-gray-600 mt-1">{funnel.description}</p>
              <div className="mt-3 flex justify-between text-sm">
                <span className={`px-2 py-1 rounded-full text-xs ${funnel.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                  {funnel.isActive ? 'Active' : 'Inactive'}
                </span>
                <span className="text-gray-500">
                  {funnel.steps.length} steps
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>

    {/* Funnel Analytics */}
    {analytics && (
      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Sessions</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalSessions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.completedSessions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.overallConversionRate}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Steps</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.stepAnalytics.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Funnel Visualization */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Funnel Performance</h3>
          {funnelChartData && (
            <div className="text-center text-gray-500 py-8">
              Funnel chart temporarily disabled for production build
            </div>
          )}
        </div>

        {/* Step-by-Step Analysis */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Step Analysis</h3>
          <div className="space-y-4">
            {analytics.stepAnalytics.map((step, index) => (
              <div key={step.stepId} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold mr-3">
                      {step.stepOrder}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{step.name}</h4>
                      <p className="text-sm text-gray-600">{step.pagePattern} • {step.eventType}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-gray-900">{step.completions}</div>
                    <div className="text-sm text-gray-600">completions</div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 mt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{step.conversionRate}%</div>
                    <div className="text-sm text-gray-600">Conversion Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{step.dropoffRate}%</div>
                    <div className="text-sm text-gray-600">Drop-off Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{step.avgTimeSpent}s</div>
                    <div className="text-sm text-gray-600">Avg Time</div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Progress</span>
                    <span>{step.conversionRate}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${step.conversionRate}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Daily Trends */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Trends</h3>
          {trendsChartData && (
            <Line data={trendsChartData} options={{
              responsive: true,
              plugins: {
                legend: {
                  position: 'top' as const,
                },
              },
              scales: {
                y: {
                  beginAtZero: true,
                },
              },
            }} />
          )}
        </div>
      </div>
    )}
  </div>
);
}
