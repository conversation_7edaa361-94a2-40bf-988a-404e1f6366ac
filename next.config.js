/** @type {import('next').NextConfig} */
const isDev = process.env.NODE_ENV !== 'production';

const nextConfig = {
  // Memory optimization for development
  reactStrictMode: false, // Reduces memory usage in dev

  // Skip TypeScript checking during build
  typescript: {
    ignoreBuildErrors: true,
  },

  // Skip ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Webpack optimizations for memory
  webpack: (config, { dev, isServer }) => {
    if (dev) {
      // Reduce memory usage in development
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            default: false,
            vendors: false,
            // Vendor chunk
            vendor: {
              name: 'vendor',
              chunks: 'all',
              test: /node_modules/,
              priority: 20
            },
            // Common chunk
            common: {
              minChunks: 2,
              chunks: 'all',
              name: 'common',
              priority: 10,
              reuseExistingChunk: true,
              enforce: true
            }
          }
        }
      };

      // Reduce webpack memory usage
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
        ignored: ['**/node_modules', '**/.next', '**/logs', '**/.git']
      };
    }
    return config;
  },

  // Force port 3000 for development
  env: {
    PORT: '3000',
  },

  // Server configuration
  serverRuntimeConfig: {
    port: 3000,
  },

  // Allow cross-origin requests from dev domain in development (Next.js 15.2.3+)
  allowedDevOrigins: isDev ? ['dev.omnilyzer.ai', 'omnilyzer.ai'] : [],

  // Explicitly define how images should be handled
  images: {
    unoptimized: true, // For static export
    domains: ['152.42.234.69', 'dev.omnilyzer.ai', 'omnilyzer.ai'],
  },

  // Define public directory explicitly
  publicRuntimeConfig: {
    staticFolder: '/public',
    port: 3000,
  },

  // Development optimizations
  ...(isDev && {
    // Disable telemetry to save memory
    telemetry: false,

    // Optimize for development
    experimental: {
      // Reduce memory usage
      optimizePackageImports: ['react', 'react-dom'],
      // Faster builds
      turbo: {
        rules: {
          '*.svg': {
            loaders: ['@svgr/webpack'],
            as: '*.js',
          },
        },
      },
    },
  }),
}

module.exports = nextConfig
